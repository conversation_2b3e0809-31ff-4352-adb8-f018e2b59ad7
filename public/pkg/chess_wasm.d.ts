/* tslint:disable */
/* eslint-disable */
export function map_detections_to_fen(homography_matrix: Float32Array, detections: Float32Array, board_size: number): string;
export function validate_fen(fen: string): boolean;
export function main(): void;
export class Detection {
  free(): void;
  constructor(x: number, y: number, width: number, height: number, class_id: number, confidence: number);
  x: number;
  y: number;
  width: number;
  height: number;
  class_id: number;
  confidence: number;
  readonly center_x: number;
  readonly center_y: number;
}

export type InitInput = RequestInfo | URL | Response | BufferSource | WebAssembly.Module;

export interface InitOutput {
  readonly memory: WebAssembly.Memory;
  readonly __wbg_detection_free: (a: number, b: number) => void;
  readonly __wbg_get_detection_x: (a: number) => number;
  readonly __wbg_set_detection_x: (a: number, b: number) => void;
  readonly __wbg_get_detection_y: (a: number) => number;
  readonly __wbg_set_detection_y: (a: number, b: number) => void;
  readonly __wbg_get_detection_width: (a: number) => number;
  readonly __wbg_set_detection_width: (a: number, b: number) => void;
  readonly __wbg_get_detection_height: (a: number) => number;
  readonly __wbg_set_detection_height: (a: number, b: number) => void;
  readonly __wbg_get_detection_class_id: (a: number) => number;
  readonly __wbg_set_detection_class_id: (a: number, b: number) => void;
  readonly __wbg_get_detection_confidence: (a: number) => number;
  readonly __wbg_set_detection_confidence: (a: number, b: number) => void;
  readonly detection_new: (a: number, b: number, c: number, d: number, e: number, f: number) => number;
  readonly detection_center_x: (a: number) => number;
  readonly detection_center_y: (a: number) => number;
  readonly map_detections_to_fen: (a: number, b: number, c: number, d: number, e: number) => [number, number];
  readonly validate_fen: (a: number, b: number) => number;
  readonly main: () => void;
  readonly __wbindgen_export_0: WebAssembly.Table;
  readonly __wbindgen_malloc: (a: number, b: number) => number;
  readonly __wbindgen_free: (a: number, b: number, c: number) => void;
  readonly __wbindgen_realloc: (a: number, b: number, c: number, d: number) => number;
  readonly __wbindgen_start: () => void;
}

export type SyncInitInput = BufferSource | WebAssembly.Module;
/**
* Instantiates the given `module`, which can either be bytes or
* a precompiled `WebAssembly.Module`.
*
* @param {{ module: SyncInitInput }} module - Passing `SyncInitInput` directly is deprecated.
*
* @returns {InitOutput}
*/
export function initSync(module: { module: SyncInitInput } | SyncInitInput): InitOutput;

/**
* If `module_or_path` is {RequestInfo} or {URL}, makes a request and
* for everything else, calls `WebAssembly.instantiate` directly.
*
* @param {{ module_or_path: InitInput | Promise<InitInput> }} module_or_path - Passing `InitInput` directly is deprecated.
*
* @returns {Promise<InitOutput>}
*/
export default function __wbg_init (module_or_path?: { module_or_path: InitInput | Promise<InitInput> } | InitInput | Promise<InitInput>): Promise<InitOutput>;
