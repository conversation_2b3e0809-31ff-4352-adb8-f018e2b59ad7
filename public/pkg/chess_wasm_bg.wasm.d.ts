/* tslint:disable */
/* eslint-disable */
export const memory: WebAssembly.Memory;
export const __wbg_detection_free: (a: number, b: number) => void;
export const __wbg_get_detection_x: (a: number) => number;
export const __wbg_set_detection_x: (a: number, b: number) => void;
export const __wbg_get_detection_y: (a: number) => number;
export const __wbg_set_detection_y: (a: number, b: number) => void;
export const __wbg_get_detection_width: (a: number) => number;
export const __wbg_set_detection_width: (a: number, b: number) => void;
export const __wbg_get_detection_height: (a: number) => number;
export const __wbg_set_detection_height: (a: number, b: number) => void;
export const __wbg_get_detection_class_id: (a: number) => number;
export const __wbg_set_detection_class_id: (a: number, b: number) => void;
export const __wbg_get_detection_confidence: (a: number) => number;
export const __wbg_set_detection_confidence: (a: number, b: number) => void;
export const detection_new: (a: number, b: number, c: number, d: number, e: number, f: number) => number;
export const detection_center_x: (a: number) => number;
export const detection_center_y: (a: number) => number;
export const map_detections_to_fen: (a: number, b: number, c: number, d: number, e: number) => [number, number];
export const validate_fen: (a: number, b: number) => number;
export const main: () => void;
export const __wbindgen_export_0: WebAssembly.Table;
export const __wbindgen_malloc: (a: number, b: number) => number;
export const __wbindgen_free: (a: number, b: number, c: number) => void;
export const __wbindgen_realloc: (a: number, b: number, c: number, d: number) => number;
export const __wbindgen_start: () => void;
