import chess
import chess.engine
import chess.polyglot
from typing import List, Optional, Tuple
import heapq
from dataclasses import dataclass, field
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Configuration constants
STOCKFISH_PATH = "stockfish"
POLYGLOT_BOOK_PATH = "Performance.bin"
MAX_DEPTH = 6
BEAM_WIDTH = 10


@dataclass
class SearchNode:
    """Represents a node in the backward search tree."""
    position: chess.Board
    cost: float
    depth: int
    parent: Optional['SearchNode'] = None
    move_from_parent: Optional[chess.Move] = None

    def __lt__(self, other):
        """For priority queue ordering (lower cost = higher priority)."""
        return self.cost < other.cost


def load_engine(stockfish_path: str) -> chess.engine.SimpleEngine:
    """
    Launch and return a Stockfish engine instance for evaluations.

    Args:
        stockfish_path: Path to the Stockfish binary.

    Returns:
        An active chess.engine.SimpleEngine instance.

    Notes:
        The engine should be terminated with .quit() after use.
    """
    try:
        engine = chess.engine.SimpleEngine.popen_uci(stockfish_path)
        logger.info(f"Stockfish engine loaded from: {stockfish_path}")
        return engine
    except Exception as e:
        logger.error(f"Failed to load Stockfish engine: {e}")
        raise


def load_opening_book(book_path: str):
    """
    Open and return a Polyglot opening book reader.

    Args:
        book_path: Path to the Polyglot book (e.g., performance.bin).

    Returns:
        A chess.polyglot.Reader instance for fast lookup of moves and weights.
    """
    try:
        book = chess.polyglot.open_reader(book_path)
        logger.info(f"Opening book loaded from: {book_path}")
        return book
    except Exception as e:
        logger.error(f"Failed to load opening book: {e}")
        raise


def find_predecessors(position: chess.Board) -> List[Tuple[chess.Board, chess.Move]]:
    """
    Generate all possible predecessor positions of the given position.

    This uses a more systematic approach by analyzing the differences between
    the current position and the starting position to generate likely moves.
    """
    predecessors = []

    # Create a board with the opposite turn to generate moves
    test_board = position.copy()
    test_board.turn = not test_board.turn

    # Strategy 1: Try simple piece moves
    # For each piece on the board, try moving it to adjacent squares
    for to_square in chess.SQUARES:
        piece = position.piece_at(to_square)
        if piece is None or piece.color == position.turn:
            continue  # Only consider pieces of the side that just moved

        # Try all possible source squares for this piece
        for from_square in chess.SQUARES:
            if from_square == to_square:
                continue

            # Create predecessor position
            pred_board = position.copy()
            pred_board.remove_piece_at(to_square)
            pred_board.set_piece_at(from_square, piece)
            pred_board.turn = not pred_board.turn

            # Check if the move is legal
            move = chess.Move(from_square, to_square)
            if move in pred_board.legal_moves:
                # Verify this move leads to our position
                verify_board = pred_board.copy()
                verify_board.push(move)
                if (verify_board.board_fen() == position.board_fen() and
                    verify_board.turn == position.turn):
                    predecessors.append((pred_board, move))

    # Strategy 2: Try captures
    # Look for empty squares that might have had captured pieces
    for capture_square in chess.SQUARES:
        if position.piece_at(capture_square) is not None:
            continue

        # Try different captured pieces
        for piece_type in [chess.PAWN, chess.KNIGHT, chess.BISHOP, chess.ROOK, chess.QUEEN]:
            captured_piece = chess.Piece(piece_type, position.turn)  # Opposite color

            # Try different attacking pieces
            for from_square in chess.SQUARES:
                attacking_piece = position.piece_at(from_square)
                if attacking_piece is None or attacking_piece.color == position.turn:
                    continue

                # Create predecessor with captured piece
                pred_board = position.copy()
                pred_board.set_piece_at(capture_square, captured_piece)
                pred_board.turn = not pred_board.turn

                # Check if capture is legal
                move = chess.Move(from_square, capture_square)
                if move in pred_board.legal_moves:
                    verify_board = pred_board.copy()
                    verify_board.push(move)
                    if (verify_board.board_fen() == position.board_fen() and
                        verify_board.turn == position.turn):
                        predecessors.append((pred_board, move))

    # Strategy 3: Try pawn moves (including double moves and en passant)
    for square in chess.SQUARES:
        piece = position.piece_at(square)
        if piece is None or piece.piece_type != chess.PAWN or piece.color == position.turn:
            continue

        # Try single pawn move
        if piece.color == chess.WHITE:
            from_square = square - 8
            if chess.square_file(from_square) == chess.square_file(square) and from_square >= 0:
                pred_board = position.copy()
                pred_board.remove_piece_at(square)
                pred_board.set_piece_at(from_square, piece)
                pred_board.turn = not pred_board.turn

                move = chess.Move(from_square, square)
                if move in pred_board.legal_moves:
                    verify_board = pred_board.copy()
                    verify_board.push(move)
                    if (verify_board.board_fen() == position.board_fen() and
                        verify_board.turn == position.turn):
                        predecessors.append((pred_board, move))

                # Try double pawn move from starting rank
                if chess.square_rank(from_square) == 1:
                    double_from = from_square - 8
                    if double_from >= 0:
                        pred_board2 = position.copy()
                        pred_board2.remove_piece_at(square)
                        pred_board2.set_piece_at(double_from, piece)
                        pred_board2.turn = not pred_board2.turn

                        double_move = chess.Move(double_from, square)
                        if double_move in pred_board2.legal_moves:
                            verify_board2 = pred_board2.copy()
                            verify_board2.push(double_move)
                            if (verify_board2.board_fen() == position.board_fen() and
                                verify_board2.turn == position.turn):
                                predecessors.append((pred_board2, double_move))
        else:  # Black pawn
            from_square = square + 8
            if chess.square_file(from_square) == chess.square_file(square) and from_square <= 63:
                pred_board = position.copy()
                pred_board.remove_piece_at(square)
                pred_board.set_piece_at(from_square, piece)
                pred_board.turn = not pred_board.turn

                move = chess.Move(from_square, square)
                if move in pred_board.legal_moves:
                    verify_board = pred_board.copy()
                    verify_board.push(move)
                    if (verify_board.board_fen() == position.board_fen() and
                        verify_board.turn == position.turn):
                        predecessors.append((pred_board, move))

                # Try double pawn move from starting rank
                if chess.square_rank(from_square) == 6:
                    double_from = from_square + 8
                    if double_from <= 63:
                        pred_board2 = position.copy()
                        pred_board2.remove_piece_at(square)
                        pred_board2.set_piece_at(double_from, piece)
                        pred_board2.turn = not pred_board2.turn

                        double_move = chess.Move(double_from, square)
                        if double_move in pred_board2.legal_moves:
                            verify_board2 = pred_board2.copy()
                            verify_board2.push(double_move)
                            if (verify_board2.board_fen() == position.board_fen() and
                                verify_board2.turn == position.turn):
                                predecessors.append((pred_board2, double_move))

    return predecessors[:20]  # Limit to most promising candidates


def evaluate_transition(prev: chess.Board, next: chess.Board, engine: chess.engine.SimpleEngine) -> float:
    """
    Compute a "transition cost" between two consecutive positions.

    Args:
        prev: The predecessor board position.
        next: The successor board position.
        engine: An active Stockfish instance to compute centipawn evaluations.

    Returns:
        A float representing the cost, e.g., abs(eval(prev) - eval(next)).

    Notes:
        A lower cost indicates a more plausible human/engine move sequence.
    """
    try:
        # Get evaluations for both positions
        prev_eval = engine.analyse(prev, chess.engine.Limit(depth=10))['score'].relative.score(mate_score=10000)
        next_eval = engine.analyse(next, chess.engine.Limit(depth=10))['score'].relative.score(mate_score=10000)

        # Calculate the absolute difference in centipawns
        if prev_eval is None:
            prev_eval = 0
        if next_eval is None:
            next_eval = 0

        cost = abs(prev_eval - next_eval)
        return cost

    except Exception as e:
        logger.warning(f"Evaluation failed: {e}")
        return 1000.0  # High cost for failed evaluations


def find_book_line_to_root(position: chess.Board, book) -> Optional[List[chess.Move]]:
    """
    Attempt to connect a position directly to the starting position using
    only book moves (Polyglot entries).

    Args:
        position: A chess.Board representing the current position.
        book: A Polyglot Reader for querying the opening book.

    Returns:
        A list of chess.Move objects forming a valid book line from the
        start position to `position`, or None if no book path is found.

    Notes:
        This avoids unnecessary search once a known opening path is found.
    """
    # Start from the initial position
    board = chess.Board()
    moves = []

    try:
        while board.board_fen() != position.board_fen():
            # Look for book moves from current position
            book_moves = list(book.find_all(board))

            if not book_moves:
                return None  # No book continuation

            # Try each book move to see if any leads toward our target
            found_move = None
            for entry in book_moves:
                test_board = board.copy()
                test_board.push(entry.move)

                # Simple heuristic: if this move gets us closer to target position
                # (measured by number of different pieces), use it
                if test_board.board_fen() == position.board_fen():
                    found_move = entry.move
                    break

                # For now, just take the highest-weighted move
                if found_move is None:
                    found_move = entry.move

            if found_move is None:
                return None

            board.push(found_move)
            moves.append(found_move)

            # Prevent infinite loops
            if len(moves) > 50:
                return None

        return moves

    except Exception as e:
        logger.warning(f"Book line search failed: {e}")
        return None


def retro_search(target: chess.Board,
                 engine: chess.engine.SimpleEngine,
                 book,
                 max_depth: int,
                 beam_width: int) -> List[List[chess.Move]]:
    """
    Perform a backward search from the target position toward the initial
    position, ranking candidate histories by plausibility.

    Args:
        target: The target chess position (Board).
        engine: Stockfish engine for evaluation.
        book: Polyglot book for early pruning/book paths.
        max_depth: Maximum backward plies to explore before giving up.
        beam_width: How many best candidates to keep at each step (beam search).

    Returns:
        A list of candidate move sequences (each as a list of chess.Move).
        The sequences are ordered from most plausible to least plausible.

    Strategy:
        - Start from `target` and generate predecessor positions.
        - Score each transition using `evaluate_transition`.
        - If a predecessor is fully inside the book, attach the corresponding
          book line and stop expanding that branch.
        - Prune aggressively using the beam_width.
    """
    logger.info(f"Starting backward search from target position with max_depth={max_depth}, beam_width={beam_width}")

    # Priority queue for beam search (min-heap based on cost)
    frontier = []

    # Initialize with target position
    start_node = SearchNode(position=target.copy(), cost=0.0, depth=0)
    heapq.heappush(frontier, start_node)

    # Track completed paths
    completed_paths = []

    # Track visited positions to avoid cycles
    visited = set()
    visited.add(target.fen())

    while frontier and len(completed_paths) < beam_width:
        # Get the most promising node
        current_node = heapq.heappop(frontier)

        logger.debug(f"Exploring node at depth {current_node.depth}, cost {current_node.cost:.2f}")

        # Check if we've reached the starting position
        if current_node.position.fen() == chess.Board().fen():
            path = reconstruct_line_from_node(current_node)
            completed_paths.append(path)
            logger.info(f"Found complete path with cost {current_node.cost:.2f}")
            continue

        # Check if we can connect to start via book moves
        book_line = find_book_line_to_root(current_node.position, book)
        if book_line is not None:
            # Reconstruct the full path: search path + book line
            search_path = reconstruct_line_from_node(current_node)
            full_path = book_line + search_path
            completed_paths.append(full_path)
            logger.info(f"Found book-connected path with cost {current_node.cost:.2f}")
            continue

        # Don't expand beyond max depth
        if current_node.depth >= max_depth:
            continue

        # Generate predecessor positions
        predecessors = find_predecessors(current_node.position)
        logger.info(f"Found {len(predecessors)} predecessors for depth {current_node.depth}")

        if len(predecessors) == 0:
            logger.info(f"No predecessors found at depth {current_node.depth}, skipping")
            continue

        # Evaluate each predecessor and add to frontier
        for pred_pos, move in predecessors:
            pred_fen = pred_pos.fen()

            # Skip if already visited
            if pred_fen in visited:
                continue

            # Calculate transition cost
            cost = evaluate_transition(pred_pos, current_node.position, engine)
            total_cost = current_node.cost + cost

            # Create new node
            pred_node = SearchNode(
                position=pred_pos,
                cost=total_cost,
                depth=current_node.depth + 1,
                parent=current_node,
                move_from_parent=move
            )

            heapq.heappush(frontier, pred_node)
            visited.add(pred_fen)

        # Prune frontier to beam width
        if len(frontier) > beam_width * 2:  # Allow some buffer
            # Keep only the best candidates
            frontier = heapq.nsmallest(beam_width, frontier)
            heapq.heapify(frontier)

    logger.info(f"Search completed. Found {len(completed_paths)} candidate paths.")
    return completed_paths


def reconstruct_line_from_node(node: SearchNode) -> List[chess.Move]:
    """
    Reconstruct the move sequence from a search node back to its root.

    Args:
        node: The final node in the search path.

    Returns:
        A list of chess.Move objects from start to target position.
    """
    moves = []
    current = node

    # Collect all moves from the path (excluding the root node which has no move)
    while current is not None and current.move_from_parent is not None:
        moves.append(current.move_from_parent)
        current = current.parent

    # Reverse to get start-to-target order
    moves.reverse()

    return moves


def reconstruct_line(history: List[chess.Board]) -> List[chess.Move]:
    """
    Convert a sequence of Board states into a list of Move objects
    representing the game line from start to target.

    Args:
        history: List of board states from start to target (inclusive).

    Returns:
        A list of chess.Move objects representing the moves played.
    """
    moves = []

    for i in range(len(history) - 1):
        current_board = history[i].copy()
        next_board = history[i + 1]

        # Find the move that transforms current to next
        for move in current_board.legal_moves:
            test_board = current_board.copy()
            test_board.push(move)
            if test_board.board_fen() == next_board.board_fen():
                moves.append(move)
                break

    return moves


def main() -> None:
    """
    Example driver function:
        - Load Stockfish and Polyglot book.
        - Define a target FEN.
        - Run retro_search() to find plausible move histories.
        - Print the top N candidate lines.
    """
    # Example target position: Complex middlegame position
    target_fen = "r1bqk2r/pppp1ppp/2n2n2/2b1p3/2B1P3/3P1N2/PPP2PPP/RNBQK2R w KQkq - 4 5"

    logger.info("Chess Position Reconstruction Demo")
    logger.info("=" * 50)
    logger.info("This demo implements backward search to find plausible game histories")
    logger.info("that lead to a given chess position using Stockfish evaluation")
    logger.info("and Polyglot opening book optimization.")
    logger.info("")
    logger.info(f"Target position: {target_fen}")

    try:
        # Load engine and book
        logger.info("Loading Stockfish engine...")
        engine = load_engine(STOCKFISH_PATH)

        logger.info("Loading opening book...")
        book = load_opening_book(POLYGLOT_BOOK_PATH)

        # Create target position
        target_board = chess.Board(target_fen)
        logger.info(f"Target position loaded:\n{target_board}")

        # Run backward search
        logger.info("Starting backward search...")
        candidate_lines = retro_search(
            target=target_board,
            engine=engine,
            book=book,
            max_depth=MAX_DEPTH,
            beam_width=BEAM_WIDTH
        )

        # Display results
        logger.info("\nSearch Results:")
        logger.info("=" * 50)

        if not candidate_lines:
            logger.info("No candidate lines found.")
        else:
            for i, line in enumerate(candidate_lines[:3], 1):  # Show top 3
                logger.info(f"\nCandidate {i}:")

                # Convert moves to SAN notation for display
                board = chess.Board()
                san_moves: list[str] = []

                for move in line:
                    san_moves.append(board.san(move))
                    board.push(move)

                # Format as numbered moves
                move_pairs: list[str] = []
                for j in range(0, len(san_moves), 2):
                    move_num = (j // 2) + 1
                    white_move = san_moves[j] if j < len(san_moves) else ""
                    black_move = san_moves[j + 1] if j + 1 < len(san_moves) else ""

                    if black_move:
                        move_pairs.append(f"{move_num}.{white_move} {black_move}")
                    else:
                        move_pairs.append(f"{move_num}.{white_move}")

                logger.info(" ".join(move_pairs))
                logger.info(f"Final position: {board.fen()}")

        # Cleanup
        engine.quit()
        book.close()

    except Exception as e:
        logger.error(f"Demo failed: {e}")
        raise


if __name__ == "__main__":
    main()