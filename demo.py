"""
retro_like_paths.py

Finds and scores every sequence (up to MAX_PLIES half‑moves)
that reaches TARGET_FEN, using
    score = |Δ eval|  – log(book_weight)
at each ply.

Requires:
    pip install python-chess stockfish

Optional:
    A Polyglot book in BIN format (e.g. 'performance.bin' from https://abrok.eu/stockfish/book/)
"""

import chess
import chess.engine
import chess.polyglot
import math
import heapq
import itertools
import collections
import os
import sys
import argparse
import logging
import shutil
from typing import List, Tuple, Optional, Dict, NamedTuple, Deque, Union
# ----------------------------------------------------------------------
# LOGGING CONFIGURATION
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%H:%M:%S'
)
logger = logging.getLogger(__name__)

# ----------------------------------------------------------------------
# CONFIG – edit these three paths if necessary
STOCKFISH: str = "stockfish"          # name or full path of the engine binary
POLYGLOT_BOOK: str = "performance.bin"  # or any other .bin; leave empty to disable
MAX_PLIES: int = 6                    # search depth

# The demo target position: 1.d4 Nf6 2.c4 e6 3.Nf3 d5   (White to move)
TARGET_FEN: str = "rnbqkb1r/ppp1pppp/4pn2/3p4/2P5/5N2/PP1P1PPP/RNBQKB1R w KQkq d6 0 4"

# ----------------------------------------------------------------------

# Define the Node type for better type hints
class Node(NamedTuple):
    fen: chess.Board
    parent: Optional[str]
    move: Optional[str]
    eval: int
    book: int
    ply: int

def book_weight(book: Optional[chess.polyglot.MemoryMappedReader], board: chess.Board) -> int:
    """Return the Polyglot weight (frequency) for *board* or 0 if not found."""
    if book is None:
        return 0
    try:
        entry = book.find(board)
        return entry.weight
    except (IndexError, AttributeError):
        logger.debug(f"No book entry found for position: {board.fen()}")
        return 0

def cp_eval(engine: chess.engine.SimpleEngine, board: chess.Board) -> int:
    """Centipawn evaluation from the side‑to‑move’s point of view."""
    logger.debug(f"Evaluating position: {board.fen()}")
    info = engine.analyse(board, chess.engine.Limit(depth=12))
    score = info["score"].pov(board.turn).score(mate_score=10_000)
    logger.debug(f"Position evaluation: {score} centipawns")
    return score

def forward_tree(start: chess.Board,
                 max_plies: int,
                 engine: chess.engine.SimpleEngine,
                 book: Optional[chess.polyglot.MemoryMappedReader] = None) -> Dict[str, Node]:
    """
    Breadth‑first expansion up to *max_plies* plies.
    Returns a dictionary mapping FEN strings to Node objects.
    """
    logger.info(f"Building game tree from starting position with max depth {max_plies}")

    root_key = start.fen()
    queue: Deque[str] = collections.deque([root_key])
    nodes: Dict[str, Node] = {root_key: Node(start, None, None,
                            cp_eval(engine, start),
                            book_weight(book, start),
                            0)}

    positions_evaluated = 0
    while queue:
        key = queue.popleft()
        node = nodes[key]
        if node.ply >= max_plies:
            continue

        # Fix: Create board from FEN string, not copy from FEN
        board = chess.Board(node.fen.fen()) if isinstance(node.fen, chess.Board) else chess.Board(node.fen)

        for move in board.legal_moves:
            child = board.copy()
            child.push(move)
            child_key = child.fen()
            if child_key in nodes:           # already visited
                continue

            positions_evaluated += 1
            if positions_evaluated % 100 == 0:
                logger.info(f"Evaluated {positions_evaluated} positions, current depth: {node.ply + 1}")

            nodes[child_key] = Node(child,
                                    key,
                                    board.san(move),
                                    cp_eval(engine, child),
                                    book_weight(book, child),
                                    node.ply+1)
            queue.append(child_key)

    logger.info(f"Tree building complete. Total positions: {len(nodes)}, evaluated: {positions_evaluated}")
    return nodes

def extract_paths(nodes: Dict[str, Node], target_fen: str) -> List[List[str]]:
    """Return every move‑sequence that reaches *target_fen*."""
    logger.info(f"Extracting paths to target position: {target_fen}")

    if target_fen not in nodes:
        logger.warning("Target FEN not found in the generated tree")
        return []

    paths: List[List[str]] = []
    stack: List[List[Union[str, List[str]]]] = [[target_fen, []]]

    while stack:
        key, acc = stack.pop()
        node = nodes[key]
        if node.parent is None:
            paths.append(acc[::-1])        # reached root
        else:
            stack.append([node.parent, acc + [node.move]])

    logger.info(f"Found {len(paths)} paths to target position")
    return paths

def path_cost(nodes: Dict[str, Node], path_fens: List[str]) -> float:
    """
    Cost = Σ |Δ eval| – log(book_wt+1) along the *positions*,
    not the moves (first element is starting position).
    """
    cost = 0.0
    for prev, nxt in zip(path_fens, path_fens[1:]):
        a, b = nodes[prev], nodes[nxt]
        eval_delta = abs(b.eval - a.eval)
        book_bonus = math.log(b.book + 1)
        cost += eval_delta           # |Δ eval|
        cost -= book_bonus           # frequent = cheaper

    logger.debug(f"Path cost calculated: {cost:.2f}")
    return cost

def rebuild_fens(path_moves: List[str]) -> List[str]:
    """Convenience: starting from root, apply *path_moves* to get FEN list."""
    board = chess.Board()
    fens: List[str] = [board.fen()]

    for san in path_moves:
        try:
            board.push_san(san)
            fens.append(board.fen())
        except ValueError as e:
            logger.error(f"Invalid move {san}: {e}")
            raise

    return fens

def main() -> None:
    """Main function to run the chess path analysis."""
    logger.info("Starting chess path analysis")

    # -------- initialise helpers --------
    logger.info("Initializing chess engine and book")

    if not shutil.which(STOCKFISH):
        logger.error(f"Cannot find '{STOCKFISH}' in PATH")
        sys.exit(f"Cannot find '{STOCKFISH}' in edit STOCKFISH variable.")

    engine: chess.engine.SimpleEngine = chess.engine.SimpleEngine.popen_uci(STOCKFISH)
    logger.info(f"Chess engine '{STOCKFISH}' initialized successfully")

    book: Optional[chess.polyglot.MemoryMappedReader] = None
    if POLYGLOT_BOOK and os.path.exists(POLYGLOT_BOOK):
        book = chess.polyglot.open_reader(POLYGLOT_BOOK)
        logger.info(f"Polyglot book '{POLYGLOT_BOOK}' loaded successfully")
    else:
        logger.info("No polyglot book available - proceeding without book moves")

    start_board: chess.Board = chess.Board()
    logger.info(f"Starting from position: {start_board.fen()}")

    logger.info("Building game tree...")
    nodes = forward_tree(start_board, MAX_PLIES, engine, book)

    logger.info(f"Found {len(nodes)} distinct positions at depth ≤ {MAX_PLIES}")
    paths = extract_paths(nodes, TARGET_FEN)
    if not paths:
        logger.warning("TARGET_FEN unreachable within depth limit")
        print("TARGET_FEN unreachable within depth limit.")
        return

    logger.info(f"Calculating costs for {len(paths)} paths")
    scored: List[Tuple[float, List[str]]] = []
    for i, moves in enumerate(paths):
        if i % 10 == 0 and i > 0:
            logger.debug(f"Processed {i}/{len(paths)} paths")
        fens = rebuild_fens(moves)
        cost = path_cost(nodes, fens)
        scored.append((cost, moves))

    scored.sort(key=lambda t: t[0])
    logger.info("Path analysis complete")

    print(f"\nTop {min(5, len(scored))} candidate line(s):")
    for rank, (c, mv) in enumerate(scored[:5], 1):
        logger.info(f"Rank {rank}: cost {c:.2f}, moves: {' '.join(mv)}")
        print(f"{rank}. cost {c:.2f} : {' '.join(mv)}")

    logger.info("Cleaning up resources")
    engine.quit()
    if book:
        book.close()
    logger.info("Analysis complete")

if __name__ == "__main__":
    main()
