// Classical computer vision-based chess piece detection using OpenCV.js
export class CVChessPieceDetector {
    constructor() {
        this.cv = null;
        this.initialized = false;
    }
    
    async initialize() {
        if (this.initialized) return true;
        
        try {
            if (typeof window !== 'undefined' && window.cv) {
                this.cv = window.cv;
                
                if (this.cv.getBuildInformation) {
                    console.log('OpenCV.js initialized for piece detection');
                    this.initialized = true;
                    return true;
                } else {
                    return new Promise((resolve) => {
                        this.cv.onRuntimeInitialized = () => {
                            console.log('OpenCV.js initialized for piece detection');
                            this.initialized = true;
                            resolve(true);
                        };
                    });
                }
            } else {
                console.warn('OpenCV.js not available for piece detection');
                return false;
            }
        } catch (error) {
            console.error('Failed to initialize OpenCV.js for piece detection:', error);
            return false;
        }
    }
    
    async detectPieces(canvas, boardCorners = null) {
        if (!await this.initialize()) {
            console.warn('OpenCV.js not available, cannot detect pieces');
            return [];
        }
        
        try {
            // Convert canvas to OpenCV Mat
            const src = this.cv.imread(canvas);
            
            // If board corners are provided, extract the board region
            let boardRegion = src;
            let useTransformedCoords = false;

            if (boardCorners && boardCorners.length === 4 &&
                Array.isArray(boardCorners) && boardCorners[0].x !== undefined) {
                try {
                    boardRegion = this.extractBoardRegion(src, boardCorners);
                    useTransformedCoords = true;
                } catch (error) {
                    console.warn('Failed to extract board region, using full image:', error);
                    boardRegion = src;
                }
            }
            
            // Detect pieces using classical computer vision
            const pieces = this.detectPiecesInRegion(boardRegion);
            
            // Clean up
            if (boardRegion !== src) {
                boardRegion.delete();
            }
            src.delete();
            
            return pieces;
            
        } catch (error) {
            console.error('Piece detection error:', error);
            return [];
        }
    }
    
    extractBoardRegion(src, corners) {
        // Create perspective transformation to extract board
        const srcPoints = this.cv.matFromArray(4, 1, this.cv.CV_32FC2, [
            corners[0].x, corners[0].y,
            corners[1].x, corners[1].y,
            corners[2].x, corners[2].y,
            corners[3].x, corners[3].y
        ]);
        
        const boardSize = 800;
        const dstPoints = this.cv.matFromArray(4, 1, this.cv.CV_32FC2, [
            0, 0,
            boardSize, 0,
            boardSize, boardSize,
            0, boardSize
        ]);
        
        const M = this.cv.getPerspectiveTransform(srcPoints, dstPoints);
        const boardRegion = new this.cv.Mat();
        this.cv.warpPerspective(src, boardRegion, M, new this.cv.Size(boardSize, boardSize));
        
        // Clean up
        srcPoints.delete();
        dstPoints.delete();
        M.delete();
        
        return boardRegion;
    }
    
    detectPiecesInRegion(boardRegion) {
        const pieces = [];
        const squareSize = boardRegion.cols / 8;
        
        // Convert to different color spaces for analysis
        const gray = new this.cv.Mat();
        const hsv = new this.cv.Mat();
        this.cv.cvtColor(boardRegion, gray, this.cv.COLOR_RGBA2GRAY);
        this.cv.cvtColor(boardRegion, hsv, this.cv.COLOR_RGBA2HSV);
        
        // Analyze each square
        for (let row = 0; row < 8; row++) {
            for (let col = 0; col < 8; col++) {
                const squareX = col * squareSize;
                const squareY = row * squareSize;
                
                // Extract square region (ensure integer coordinates)
                const squareRect = new this.cv.Rect(
                    Math.floor(squareX),
                    Math.floor(squareY),
                    Math.floor(squareSize),
                    Math.floor(squareSize)
                );
                const squareGray = gray.roi(squareRect);
                const squareHSV = hsv.roi(squareRect);
                
                // Detect if there's a piece in this square
                const pieceInfo = this.analyzePieceInSquare(squareGray, squareHSV, row, col);
                
                if (pieceInfo) {
                    pieces.push({
                        x: squareX + squareSize * 0.1,
                        y: squareY + squareSize * 0.1,
                        width: squareSize * 0.8,
                        height: squareSize * 0.8,
                        class_id: pieceInfo.class_id,
                        confidence: pieceInfo.confidence,
                        square: `${String.fromCharCode(97 + col)}${8 - row}` // e.g., "e4"
                    });
                }
                
                // Clean up
                squareGray.delete();
                squareHSV.delete();
            }
        }
        
        // Clean up
        gray.delete();
        hsv.delete();
        
        return pieces;
    }
    
    analyzePieceInSquare(squareGray, squareHSV, row, col) {
        // Calculate statistics for the square
        const mean = this.cv.mean(squareGray);
        const stdDev = new this.cv.Mat();
        const meanMat = new this.cv.Mat();
        this.cv.meanStdDev(squareGray, meanMat, stdDev);
        
        const avgIntensity = mean[0];
        const variance = stdDev.data64F[0] ** 2;
        
        // Clean up temporary matrices
        stdDev.delete();
        meanMat.delete();
        
        // Determine if square is light or dark (chess board pattern)
        const isLightSquare = (row + col) % 2 === 0;
        
        // Threshold for piece detection based on contrast with expected square color
        const expectedIntensity = isLightSquare ? 200 : 80; // Light vs dark square
        const intensityDiff = Math.abs(avgIntensity - expectedIntensity);
        
        // If there's significant difference from expected square color, likely a piece
        if (intensityDiff > 30 && variance > 100) {
            // Determine piece color based on intensity
            const isPieceLight = avgIntensity > 128;
            
            // Estimate piece type based on shape analysis
            const pieceType = this.estimatePieceType(squareGray, isPieceLight);
            
            // Calculate confidence based on how different it is from empty square
            const confidence = Math.min(0.9, intensityDiff / 100 + variance / 1000);
            
            return {
                class_id: this.getPieceClassId(pieceType, isPieceLight),
                confidence: confidence
            };
        }
        
        return null; // No piece detected
    }
    
    estimatePieceType(squareGray, isPieceLight) {
        // Simple shape analysis to estimate piece type
        // This is a basic implementation - could be improved with more sophisticated analysis
        
        // Find contours
        const binary = new this.cv.Mat();
        this.cv.threshold(squareGray, binary, 0, 255, this.cv.THRESH_BINARY + this.cv.THRESH_OTSU);
        
        const contours = new this.cv.MatVector();
        const hierarchy = new this.cv.Mat();
        this.cv.findContours(binary, contours, hierarchy, this.cv.RETR_EXTERNAL, this.cv.CHAIN_APPROX_SIMPLE);
        
        let pieceType = 'pawn'; // Default
        
        if (contours.size() > 0) {
            // Get the largest contour (likely the piece)
            let maxArea = 0;
            let maxContour = null;
            
            for (let i = 0; i < contours.size(); i++) {
                const contour = contours.get(i);
                const area = this.cv.contourArea(contour);
                if (area > maxArea) {
                    maxArea = area;
                    maxContour = contour;
                }
            }
            
            if (maxContour) {
                // Analyze shape characteristics
                const perimeter = this.cv.arcLength(maxContour, true);
                const area = this.cv.contourArea(maxContour);
                const circularity = 4 * Math.PI * area / (perimeter * perimeter);
                
                // Get bounding rectangle
                const boundingRect = this.cv.boundingRect(maxContour);
                const aspectRatio = boundingRect.width / boundingRect.height;
                
                // Simple heuristics for piece type
                if (circularity > 0.7) {
                    pieceType = 'pawn'; // Round-ish pieces
                } else if (aspectRatio > 1.2) {
                    pieceType = 'rook'; // Wide pieces
                } else if (area > squareGray.rows * squareGray.cols * 0.3) {
                    pieceType = 'queen'; // Large pieces
                } else {
                    pieceType = 'knight'; // Complex shapes
                }
            }
        }
        
        // Clean up
        binary.delete();
        contours.delete();
        hierarchy.delete();
        
        return pieceType;
    }
    
    getPieceClassId(pieceType, isLight) {
        // Map piece type and color to class ID
        const pieceMap = {
            'pawn': isLight ? 0 : 6,
            'knight': isLight ? 1 : 7,
            'bishop': isLight ? 2 : 8,
            'rook': isLight ? 3 : 9,
            'queen': isLight ? 4 : 10,
            'king': isLight ? 5 : 11
        };
        
        return pieceMap[pieceType] || (isLight ? 0 : 6); // Default to pawn
    }
}
