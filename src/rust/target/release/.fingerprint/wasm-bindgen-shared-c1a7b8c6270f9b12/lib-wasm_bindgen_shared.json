{"rustc": 14016512169814189693, "features": "[]", "declared_features": "[]", "target": 8958406094080315647, "profile": 4305681822225477910, "path": 12294330936635220719, "deps": [[1988483478007900009, "unicode_ident", false, 1622508851291848357], [14299170049494554845, "build_script_build", false, 3922517445396692916]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/wasm-bindgen-shared-c1a7b8c6270f9b12/dep-lib-wasm_bindgen_shared", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}