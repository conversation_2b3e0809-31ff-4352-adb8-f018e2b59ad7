{"rustc": 14016512169814189693, "features": "[\"clone-impls\", \"default\", \"derive\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"visit\", \"visit-mut\"]", "declared_features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"test\", \"visit\", \"visit-mut\"]", "target": 9442126953582868550, "profile": 1369601567987815722, "path": 12164184602866630358, "deps": [[1988483478007900009, "unicode_ident", false, 1622508851291848357], [3060637413840920116, "proc_macro2", false, 12904501471784732934], [17990358020177143287, "quote", false, 4848177533911303648]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/syn-25847fe815647aa2/dep-lib-syn", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}