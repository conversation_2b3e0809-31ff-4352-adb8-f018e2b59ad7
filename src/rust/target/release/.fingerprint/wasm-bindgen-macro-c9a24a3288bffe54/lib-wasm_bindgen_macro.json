{"rustc": 14016512169814189693, "features": "[]", "declared_features": "[\"strict-macro\", \"xxx_debug_only_print_generated_code\"]", "target": 6875603382767429092, "profile": 4305681822225477910, "path": 7471923519970828743, "deps": [[2589611628054203282, "wasm_bindgen_macro_support", false, 12801406037488433674], [17990358020177143287, "quote", false, 4848177533911303648]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/wasm-bindgen-macro-c9a24a3288bffe54/dep-lib-wasm_bindgen_macro", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}