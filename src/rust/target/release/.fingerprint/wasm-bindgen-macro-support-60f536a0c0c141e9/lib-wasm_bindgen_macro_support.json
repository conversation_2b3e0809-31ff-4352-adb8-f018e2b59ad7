{"rustc": 14016512169814189693, "features": "[]", "declared_features": "[\"extra-traits\", \"strict-macro\"]", "target": 17930477452216118438, "profile": 4305681822225477910, "path": 10267173848516180252, "deps": [[3060637413840920116, "proc_macro2", false, 12904501471784732934], [4974441333307933176, "syn", false, 18012370653424220070], [14299170049494554845, "wasm_bindgen_shared", false, 13742023766424034546], [14372503175394433084, "wasm_bindgen_backend", false, 9815923294379519281], [17990358020177143287, "quote", false, 4848177533911303648]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/wasm-bindgen-macro-support-60f536a0c0c141e9/dep-lib-wasm_bindgen_macro_support", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}