{"rustc": 14016512169814189693, "features": "[\"default\", \"msrv\", \"rustversion\", \"std\"]", "declared_features": "[\"default\", \"enable-interning\", \"gg-alloc\", \"msrv\", \"rustversion\", \"serde\", \"serde-serialize\", \"serde_json\", \"spans\", \"std\", \"strict-macro\", \"xxx_debug_only_print_generated_code\"]", "target": 4070942113156591848, "profile": 8678796594883496969, "path": 4413582070070179740, "deps": [[2828590642173593838, "cfg_if", false, 16447553200940170448], [3722963349756955755, "once_cell", false, 10499239968131593728], [6946689283190175495, "build_script_build", false, 9908708630629866225], [7858942147296547339, "rustversion", false, 11233988867704307285], [11382113702854245495, "wasm_bindgen_macro", false, 13140492954621532986]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/release/.fingerprint/wasm-bindgen-6190597540784795/dep-lib-wasm_bindgen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 14682669768258224367}