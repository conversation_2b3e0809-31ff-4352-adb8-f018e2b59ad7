version = 1
revision = 2
requires-python = ">=3.11"

[[package]]
name = "chess"
version = "1.11.2"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/93/09/7d04d7581ae3bb8b598017941781bceb7959dd1b13e3ebf7b6a2cd843bc9/chess-1.11.2.tar.gz", hash = "sha256:a8b43e5678fdb3000695bdaa573117ad683761e5ca38e591c4826eba6d25bb39", size = 6131385, upload-time = "2025-02-25T19:10:27.328Z" }

[[package]]
name = "chess-ai"
version = "0.1.0"
source = { virtual = "." }
dependencies = [
    { name = "python-chess" },
    { name = "stockfish" },
]

[package.metadata]
requires-dist = [
    { name = "python-chess", specifier = ">=1.999" },
    { name = "stockfish", specifier = ">=3.28.0" },
]

[[package]]
name = "python-chess"
version = "1.999"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "chess" },
]
sdist = { url = "https://files.pythonhosted.org/packages/5f/60/7c7d132b6683ff215bf705fc55ffc0240a6cddea89657407ca0a4fb628d0/python-chess-1.999.tar.gz", hash = "sha256:8cad0388c42242d890ac6368ad64def15cd0165db033df0ad479492e266e5e6c", size = 1453, upload-time = "2020-10-26T11:30:10.118Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/38/47/dfebc06e589530691d33c71bc7b3d8d311252b58ae338980fd4327fe77f2/python_chess-1.999-py3-none-any.whl", hash = "sha256:93b562f8f1124cb7bf56fb095e18743758e69dc6a028ccda0badcaa5c59d88c8", size = 1401, upload-time = "2020-10-26T11:30:07.758Z" },
]

[[package]]
name = "stockfish"
version = "3.28.0"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/dc/bd/b06883b957530867179da3672fa3d8b87a08a1e3c11ca9737d94b60689d2/stockfish-3.28.0.tar.gz", hash = "sha256:8764127c00434aa85b7fca1c064800e7ea907bb3626ccd4b583f0e911b014070", size = 16264, upload-time = "2022-07-05T04:38:28.217Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/95/b2/b3a204dd7754685fef5e62bf19b82d2572b321df619775502326cf7d383e/stockfish-3.28.0-py3-none-any.whl", hash = "sha256:e432e57112448fe1271dff402db3a4747bf00d1721815ce0a36cfa26582cf360", size = 13907, upload-time = "2022-07-05T04:38:25.91Z" },
]
